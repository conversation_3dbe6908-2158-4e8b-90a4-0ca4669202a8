<template>
    <FormAction
        :pre-requisites="true"
        @setPreRequisites="setPreRequisites"
        :init-url="initUrl"
        :init-form="initForm"
        :form="form"
        :set-form="setForm"
        redirect="ExamQuestionBank"
    >
        <div class="grid grid-cols-4 gap-6">
            <div class="col-span-4 sm:col-span-2">
                <BaseSelect
                    v-model="form.subject"
                    name="subject"
                    :label="$trans('exam.question_bank.props.subject')"
                    label-prop="name"
                    value-prop="uuid"
                    :options="preRequisites.subjects"
                    v-model:error="formErrors.subject"
                    required
                />
            </div>
            <div class="col-span-4 sm:col-span-1">
                <BaseSelect
                    v-model="form.type"
                    name="type"
                    :label="$trans('exam.question_bank.props.type')"
                    :options="preRequisites.types"
                    v-model:error="formErrors.type"
                    required
                />
            </div>
            <div class="col-span-4 sm:col-span-2">
                <BaseSelectSearch
                    v-if="fetchData.isLoaded"
                    multiple
                    name="batches"
                    :label="$trans('academic.batch.batch')"
                    v-model="form.batches"
                    v-model:error="formErrors.batches"
                    value-prop="uuid"
                    :init-search="fetchData.batches"
                    search-key="course_batch"
                    search-action="academic/batch/list"
                >
                    <template #selectedOption="slotProps">
                        {{ slotProps.value.course.name }} -
                        {{ slotProps.value.name }}
                    </template>

                    <template #listOption="slotProps">
                        {{ slotProps.option.course.nameWithTerm }} -
                        {{ slotProps.option.name }}
                    </template>
                </BaseSelectSearch>
            </div>
            <div class="col-span-4 sm:col-span-3">
                <BaseEditor
                    v-model="form.title"
                    name="title"
                    :edit="true"
                    toolbar="minimal"
                    :label="$trans('exam.question_bank.props.title')"
                    v-model:error="formErrors.title"
                    :height="100"
                />
            </div>
            <div class="col-span-4 sm:col-span-1">
                <BaseInput
                    type="number"
                    v-model="form.mark"
                    name="mark"
                    :label="$trans('exam.question_bank.props.mark')"
                    v-model:error="formErrors.mark"
                    step="0.01"
                    min="0.01"
                    required
                />
            </div>
            <div class="col-span-4">
                <BaseEditor
                    v-model="form.header"
                    name="header"
                    :edit="true"
                    toolbar="minimal"
                    :label="$trans('exam.question_bank.props.header')"
                    v-model:error="formErrors.header"
                    :height="100"
                />
            </div>
            <div class="col-span-4">
                <BaseTextarea
                    v-model="form.description"
                    name="description"
                    :label="$trans('exam.question_bank.props.description')"
                    v-model:error="formErrors.description"
                    :rows="4"
                />
            </div>
        </div>

        <!-- MCQ Options -->
        <div v-if="form.type === 'mcq'" class="mt-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium">
                    {{ $trans('exam.question_bank.props.options') }}
                </h3>
                <BaseButton
                    type="button"
                    variant="soft"
                    @click="addOption"
                >
                    <i class="fas fa-plus mr-2"></i>
                    {{ $trans('exam.question_bank.add_option') }}
                </BaseButton>
            </div>

            <div v-if="formErrors.options" class="text-red-500 text-sm mb-4">
                {{ formErrors.options }}
            </div>

            <div class="space-y-4">
                <div
                    v-for="(option, index) in form.options"
                    :key="index"
                    class="flex items-center space-x-4 p-4 border rounded-lg"
                >
                    <div class="flex-1">
                        <BaseInput
                            type="text"
                            v-model="option.title"
                            :name="`option_${index}`"
                            :label="$trans('exam.question_bank.option_text')"
                            :error="formErrors[`options.${index}.title`]"
                            required
                        />
                    </div>
                    <div class="flex items-center space-x-2">
                        <BaseSwitch
                            vertical
                            v-model="option.isCorrect"
                            :name="`option_correct_${index}`"
                            :label="$trans('exam.question_bank.correct_answer')"
                            @change="onCorrectAnswerChange(index)"
                        />
                        <BaseButton
                            type="button"
                            variant="soft"
                            size="sm"
                            @click="removeOption(index)"
                            v-if="form.options.length > 1"
                        >
                            <i class="fas fa-trash"></i>
                        </BaseButton>
                    </div>
                </div>
            </div>
        </div>

        <!-- Single Line Question Answer -->
        <div v-if="form.type === 'single_line_question'" class="mt-6">
            <div class="space-y-4">
                <div class="mb-4">
                    <BaseSwitch
                        v-model="form.answerConfig.autoEvaluate"
                        name="autoEvaluate"
                        :label="$trans('exam.question_bank.auto_evaluate')"
                        :description="$trans('exam.question_bank.auto_evaluate_help')"
                    />
                </div>

                <div v-if="form.answerConfig.autoEvaluate">
                    <BaseInput
                        type="text"
                        v-model="form.answer"
                        name="answer"
                        :label="$trans('exam.question_bank.props.answer')"
                        v-model:error="formErrors.answer"
                        :placeholder="$trans('exam.question_bank.answer_placeholder')"
                    />

                    <div class="mt-4 p-4 bg-gray-50 rounded-lg">
                        <h4 class="text-sm font-medium mb-3">
                            {{ $trans('exam.question_bank.matching_options') }}
                        </h4>

                        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                            <div>
                                <BaseSwitch
                                    v-model="form.answerConfig.strictMatch"
                                    name="strictMatch"
                                    :label="$trans('exam.question_bank.strict_match')"
                                />
                                <div class="text-sm text-gray-500 mt-1">
                                    {{ $trans('exam.question_bank.strict_match_help') }}
                                </div>
                            </div>

                            <div>
                                <BaseSwitch
                                    v-model="form.answerConfig.caseSensitive"
                                    name="caseSensitive"
                                    :label="$trans('exam.question_bank.case_sensitive')"
                                />
                                <div class="text-sm text-gray-500 mt-1">
                                    {{ $trans('exam.question_bank.case_sensitive_help') }}
                                </div>
                            </div>

                            <div>
                                <BaseSwitch
                                    v-model="form.answerConfig.ignoreSpaces"
                                    name="ignoreSpaces"
                                    :label="$trans('exam.question_bank.ignore_spaces')"
                                />
                                <div class="text-sm text-gray-500 mt-1">
                                    {{ $trans('exam.question_bank.ignore_spaces_help') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </FormAction>
</template>

<script>
export default {
    name: "ExamQuestionBankForm",
}
</script>

<script setup>
import { reactive, watch } from "vue"
import { useRoute } from "vue-router";
import { cloneDeep } from "@core/utils"
import { getFormErrors } from "@core/helpers/action"

const route = useRoute()

const initForm = {
    subject: "",
    batches: [],
    type: "",
    title: "",
    mark: 1,
    header: "",
    description: "",
    options: [
        { title: "", isCorrect: false },
        { title: "", isCorrect: false },
    ],
    answer: "",
    answerConfig: {
        autoEvaluate: false,
        strictMatch: false,
        caseSensitive: false,
        ignoreSpaces: true,
    },
}

const form = reactive(cloneDeep(initForm))
const formErrors = getFormErrors("exam/questionBank/")
const preRequisites = reactive({
    subjects: [],
    types: [],
})
const fetchData = reactive({
    batches: [],
    isLoaded: route.params.uuid ? false : true,
})

const initUrl = "exam/questionBank/"

const setPreRequisites = (data) => {
    Object.assign(preRequisites, data)
    if(!route.params.uuid){
        fetchData.isLoaded = true
    }
}

const setForm = (data) => {
    let batchUuids = data.batches?.map(batch => batch.uuid) || []

    // Ensure options are properly structured
    let options = data.options && data.options.length > 0
        ? cloneDeep(data.options)
        : [
            { title: "", isCorrect: false },
            { title: "", isCorrect: false },
        ]

    // Handle answer config with defaults
    let answerConfig = {
        autoEvaluate: data.answerConfig?.autoEvaluate ?? false,
        strictMatch: data.answerConfig?.strictMatch ?? false,
        caseSensitive: data.answerConfig?.caseSensitive ?? false,
        ignoreSpaces: data.answerConfig?.ignoreSpaces ?? true,
    }

    Object.assign(initForm, {
        ...data,
        subject: data.subject?.uuid || "",
        batches: batchUuids,
        type: data.type?.value || "",
        options: options,
        answer: data.answer || "",
        answerConfig: answerConfig,
    })
    Object.assign(form, cloneDeep(initForm))

    // Set the batch UUIDs for BaseSelectSearch init-search (not objects)
    fetchData.batches = batchUuids
    fetchData.isLoaded = true
}

const addOption = () => {
    form.options.push({ title: "", isCorrect: false })
}

const removeOption = (index) => {
    if (form.options.length > 1) {
        form.options.splice(index, 1)
    }
}

const onCorrectAnswerChange = (currentIndex) => {
    if (form.options[currentIndex].isCorrect) {
        form.options.forEach((option, index) => {
            if (index !== currentIndex) {
                option.isCorrect = false
            }
        })
    }
}

// Watch for type changes to reset options and answer fields
watch(() => form.type, (newType) => {
    if (newType === 'mcq' && (!form.options || form.options.length === 0)) {
        form.options = [
            { title: "", isCorrect: false },
            { title: "", isCorrect: false },
        ]
    }

    // Reset answer fields when switching away from single line questions
    if (newType !== 'single_line_question') {
        form.answer = ""
        form.answerConfig = {
            autoEvaluate: false,
            strictMatch: false,
            caseSensitive: false,
            ignoreSpaces: true,
        }
    }
})
</script>
