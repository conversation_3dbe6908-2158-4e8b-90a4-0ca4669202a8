<template>
    <ListItem :init-url="initUrl" @setItems="setItems">
        <template #header>
            <PageHeader
                :title="$trans('exam.question_bank.question_bank')"
                :navs="[{ label: $trans('exam.exam'), path: 'Exam' }]"
            >
                <PageHeaderAction
                    url="exam/question-banks/"
                    name="ExamQuestionBank"
                    :title="$trans('exam.question_bank.question_bank')"
                    :actions="userActions"
                    :dropdown-actions="dropdownActions"
                    @toggleFilter="showFilter = !showFilter"
                    @toggleImport="showImport = !showImport"
                />
            </PageHeader>
        </template>

        <template #import>
            <ParentTransition appear :visibility="showImport">
                <QuestionBankImport
                    @cancelled="showImport = false"
                    @hide="showImport = false"
                    @completed="handleImportCompleted"
                />
            </ParentTransition>
        </template>

        <template #filter>
            <ParentTransition appear :visibility="showFilter">
                <FilterForm
                    @refresh="emitter.emit('listItems')"
                    :pre-requisites="preRequisites"
                    @hide="showFilter = false"
                ></FilterForm>
            </ParentTransition>
        </template>

        <ParentTransition appear :visibility="true">
            <DataTable
                :header="questionBanks.headers"
                :meta="questionBanks.meta"
                module="exam.question_bank"
                @refresh="emitter.emit('listItems')"
            >
                <DataRow
                    v-for="questionBank in questionBanks.data"
                    :key="questionBank.uuid"
                    @double-click="
                        router.push({
                            name: 'ExamQuestionBankShow',
                            params: { uuid: questionBank.uuid },
                        })
                    "
                >
                    <DataCell name="title">
                        <div v-html="questionBank.title"></div>
                        <TextMuted block>{{
                            questionBank.type?.label
                        }}</TextMuted>
                    </DataCell>
                    <DataCell name="subject">
                        {{ questionBank.subject?.name || "-" }}
                    </DataCell>
                    <!-- <DataCell name="class">
                        {{ questionBank.class || '-' }}
                    </DataCell> -->
                    <DataCell name="batches">
                        <div v-for="batch in questionBank.batches" :key="batch.uuid">
                            {{ batch.course?.name }} - {{ batch.name }}
                        </div>
                        <span v-if="!questionBank.batches?.length">-</span>
                    </DataCell>
                    <DataCell name="mark">
                        {{ questionBank.mark }}
                    </DataCell>
                    <DataCell name="createdAt">
                        {{ questionBank.createdAt?.formatted }}
                    </DataCell>
                    <DataCell name="action">
                        <FloatingMenu>
                            <FloatingMenuItem
                                icon="fas fa-arrow-circle-right"
                                @click="
                                    router.push({
                                        name: 'ExamQuestionBankShow',
                                        params: { uuid: questionBank.uuid },
                                    })
                                "
                            >
                                {{ $trans("general.show") }}
                            </FloatingMenuItem>
                            <FloatingMenuItem
                                v-if="perform('question-bank:edit')"
                                icon="fas fa-edit"
                                @click="
                                    router.push({
                                        name: 'ExamQuestionBankEdit',
                                        params: { uuid: questionBank.uuid },
                                    })
                                "
                            >
                                {{ $trans("general.edit") }}
                            </FloatingMenuItem>
                            <FloatingMenuItem
                                v-if="perform('question-bank:create')"
                                icon="fas fa-copy"
                                @click="
                                    router.push({
                                        name: 'ExamQuestionBankDuplicate',
                                        params: { uuid: questionBank.uuid },
                                    })
                                "
                            >
                                {{ $trans("general.duplicate") }}
                            </FloatingMenuItem>
                            <FloatingMenuItem
                                v-if="perform('question-bank:delete')"
                                icon="fas fa-trash"
                                @click="
                                    emitter.emit('deleteItem', {
                                        uuid: questionBank.uuid,
                                    })
                                "
                            >
                                {{ $trans("general.delete") }}
                            </FloatingMenuItem>
                        </FloatingMenu>
                    </DataCell>
                </DataRow>
                <template #actionButton>
                    <BaseButton
                        v-if="perform('question-bank:create')"
                        @click="router.push({ name: 'ExamQuestionBankCreate' })"
                    >
                        {{
                            $trans("global.add", {
                                attribute: $trans("exam.question_bank.question_bank"),
                            })
                        }}
                    </BaseButton>
                </template>
            </DataTable>
        </ParentTransition>


    </ListItem>
</template>

<script>
export default {
    name: "ExamQuestionBankIndex",
}
</script>

<script setup>
import { ref, reactive, inject, onMounted } from "vue"
import { useRouter } from "vue-router"
import { useStore } from "vuex"
import { perform } from "@core/helpers/action"
import FilterForm from "./Filter.vue"
import QuestionBankImport from "./Import.vue"

const router = useRouter()
const store = useStore()

const emitter = inject("emitter")
const $trans = inject("$trans")

let userActions = ["filter"]
if (perform("question-bank:create")) {
    userActions.unshift("create")
}

let dropdownActions = []
if (perform("question-bank:create")) {
    dropdownActions.push("import")
}
if (perform("question-bank:export")) {
    dropdownActions.push("print", "pdf", "excel")
}

const showFilter = ref(false)
const showImport = ref(false)
const questionBanks = reactive({
    data: [],
    headers: [],
    meta: {},
})

const preRequisites = reactive({
    subjects: [],
    types: [],
    batches: [],
})

const initUrl = "exam/questionBank/"

const setItems = (data) => {
    Object.assign(questionBanks, data)
}

const handleImportCompleted = () => {
    showImport.value = false
    emitter.emit('listItems')
}

const loadPreRequisites = async () => {
    try {
        const response = await store.dispatch("exam/questionBank/preRequisiteForFilter", {})
        Object.assign(preRequisites, response)
    } catch (error) {
        console.error("Failed to load prerequisites:", error)
    }
}

onMounted(async () => {
    await loadPreRequisites()
})
</script>
