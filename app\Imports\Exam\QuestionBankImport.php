<?php

namespace App\Imports\Exam;

use App\Concerns\ItemImport;
use App\Enums\Exam\OnlineExamQuestionType;
use App\Models\Academic\Batch;
use App\Models\Academic\Course;
use App\Models\Academic\Subject;
use App\Models\Exam\QuestionBank;
use Illuminate\Support\Collection;
use Illuminate\Validation\ValidationException;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Illuminate\Support\Str;

class QuestionBankImport implements ToCollection, WithHeadingRow
{
    use ItemImport;

    protected $limit = 500;

    public function collection(Collection $rows)
    {
        if (count($rows) > $this->limit) {
            throw ValidationException::withMessages(['message' => trans('general.errors.max_import_limit_crossed', ['attribute' => $this->limit])]);
        }

        $logFile = $this->getLogFile('question_bank');

        $errors = $this->validate($rows);

        $this->checkForErrors('question_bank', $errors);

        if (! request()->boolean('validate') && ! \Storage::disk('local')->exists($logFile)) {
            $this->import($rows);
        }
    }

    public function validate(Collection $rows): array
    {
        $errors = [];


        foreach ($rows as $index => $row) {
            //$rowErrors = [];
            $rowNo = $index + 2;

            // Required fields validation
            if (empty($row['subject'])) {
                $errors[] = $this->setError($rowNo, 'Subject', 'required');
            }

            if (empty($row['type'])) {
                $errors[] = $this->setError($rowNo, 'Type', 'required');
            }

            if (empty($row['question'])) {
                $errors[] = $errors[] = $this->setError($rowNo, 'Question', 'required');
            }

            if (empty($row['mark']) || !is_numeric($row['mark']) || $row['mark'] <= 0) {
                $errors[] = $errors[] = $this->setError($rowNo, 'Mark (Required and must be a positive number)', 'invalid');
            }

            // Type validation
            if (!empty($row['type'])) {
                $validTypes = ['mcq', 'single_line_question', 'multi_line_question'];
                if (!in_array(strtolower($row['type']), $validTypes)) {
                    $errors[] = $this->setError($rowNo, 'Type (must be one of: ' . implode(', ', $validTypes).')', 'invalid');
                }
            }

            // Subject validation
            if (!empty($row['subject'])) {
                $subject = Subject::query()
                    ->byPeriod()
                    //case insensitive
                    ->whereRaw('LOWER(name) = ?', [strtolower($row['subject'])])
                    //->where('name', $row['subject'])
                    ->first();

                if (!$subject) {
                    $errors[] = $this->setError($rowNo, 'Subject (Not Found)', 'invalid');
                }
            }

            // Class validation (Course-Batch format)
            if (!empty($row['class'])) {
                $classValidation = $this->validateClass($row['class']);
                if (!$classValidation['valid']) {
                    foreach ($classValidation['errors'] as $error) {
                        $errors[] = $this->setError($rowNo, $error, 'invalid');
                    }
                }
            }

            // MCQ specific validation
            if (!empty($row['type']) && strtolower($row['type']) === 'mcq') {
                $options = $this->parseOptions($row);
                if (empty($options)) {
                    //$rowErrors[] = 'MCQ questions must have at least one option';
                    $errors[] = $this->setError($rowNo, 'MCQ questions must have at least one option', 'invalid');
                } else {
                    $correctCount = 0;
                    foreach ($options as $option) {
                        if ($option['is_correct']) {
                            $correctCount++;
                        }
                    }
                    if ($correctCount === 0) {
                        //$rowErrors[] = 'MCQ questions must have exactly one correct answer';
                        $errors[] = $this->setError($rowNo, 'MCQ questions must have exactly one correct answer', 'invalid');
                    } elseif ($correctCount > 1) {
                        //$rowErrors[] = 'MCQ questions can only have one correct answer';
                        $errors[] = $this->setError($rowNo, 'MCQ questions can only have one correct answer', 'invalid');
                    }
                }
            }

            // Single line question validation
            if (!empty($row['type']) && strtolower($row['type']) === 'single_line_question') {
                if (!empty($row['answer']) && empty($row['auto_evaluate'])) {
                    // If answer is provided, auto_evaluate should be specified
                    //$rowErrors[] = 'When answer is provided for single line questions, auto_evaluate must be specified (true/false)';
                    $errors[] = $this->setError($rowNo, 'Auto Evaluate (When answer is provided for single line questions, auto_evaluate must be specified (true/false))', 'invalid');
                }
            }

            // if (!empty($rowErrors)) {
            //     $errors[] = [
            //         'row' => $index + 2, // +2 because index starts from 0 and we have header row
            //         'errors' => $rowErrors,
            //     ];
            // }
        }


        return $errors;
    }

    public function import(Collection $rows): void
    {
        foreach ($rows as $row) {
            $this->importRow($row);
        }
    }

    private function importRow($row): void
    {
        $subject = Subject::query()
            ->byPeriod()
            //->where('name', $row['subject'])
            //case insensitive
            ->whereRaw('LOWER(name) = ?', [strtolower($row['subject'])])
            ->first();

        if (!$subject) {
            return; // Skip if subject not found
        }

        $type = strtolower($row['type']);
        $questionData = [
            'subject_id' => $subject->id,
            'type' => $type,
            'title' => $row['question'],
            'header' => $row['header'] ?? null,
            'description' => $row['description'] ?? null,
            'mark' => (float) $row['mark'],
        ];

        // Handle MCQ options
        if ($type === 'mcq') {
            $questionData['options'] = $this->parseOptions($row);
        }

        // Handle single line question answer
        if ($type === 'single_line_question') {
            $questionData['answer'] = $row['answer'] ?? null;

            if (!empty($row['answer'])) {
                $questionData['answer_config'] = [
                    'auto_evaluate' => $this->parseBoolean($row['auto_evaluate'] ?? 'false'),
                    'strict_match' => $this->parseBoolean($row['strict_match'] ?? 'false'),
                    'case_sensitive' => $this->parseBoolean($row['case_sensitive'] ?? 'false'),
                    'ignore_spaces' => $this->parseBoolean($row['ignore_spaces'] ?? 'true'),
                ];
            }
        }

        $questionBank = QuestionBank::create($questionData);

        // Handle class (course-batch) association
        if (!empty($row['class'])) {
            $classValidation = $this->validateClass($row['class']);
            if ($classValidation['valid'] && isset($classValidation['batch'])) {
                $questionBank->batches()->attach([
                    $classValidation['batch']->id => [
                        'uuid' => (string) Str::uuid(),
                    ]
                ]);
            }
        }
    }

    private function parseOptions($row): array
    {
        $options = [];
        
        // Parse up to 6 options (option_1 to option_6)
        for ($i = 1; $i <= 6; $i++) {
            $optionKey = "option_{$i}";
            $correctKey = "option_{$i}_correct";
            
            if (!empty($row[$optionKey])) {
                $options[] = [
                    'title' => $row[$optionKey],
                    'is_correct' => $this->parseBoolean($row[$correctKey] ?? 'false'),
                ];
            }
        }

        return $options;
    }

    private function parseBoolean($value): bool
    {
        if (is_bool($value)) {
            return $value;
        }

        $value = strtolower(trim($value));
        return in_array($value, ['true', '1', 'yes', 'y']);
    }

    /**
     * Validate class field in format "Course Name-Batch Name"
     */
    private function validateClass(string $class): array
    {
        $result = [
            'valid' => false,
            'errors' => [],
            'course' => null,
            'batch' => null,
        ];

        // Check if class contains the separator '-'
        if (!str_contains($class, '-')) {
            $result['errors'][] = 'Class format should be "Class Name-Class Arm Name". Found: ' . $class;
            return $result;
        }

        // Split by the last occurrence of '-' to handle course names that might contain '-'
        $parts = explode('-', $class);
        if (count($parts) < 2) {
            $result['errors'][] = 'Class format should be "Class Name-Class Aem Name". Found: ' . $class;
            return $result;
        }

        // Get course name (everything except the last part) and batch name (last part)
        $batchName = trim(array_pop($parts));
        $courseName = trim(implode('-', $parts));

        if (empty($courseName) || empty($batchName)) {
            $result['errors'][] = 'Both Class Name and Class Arm Name are required in class field. Found: ' . $class;
            return $result;
        }

        // Find the course
        $course = Course::query()
            ->byPeriod()
            ->where('name', $courseName)
            ->first();

        if (!$course) {
            $result['errors'][] = 'Class not found: ' . $courseName;
            return $result;
        }

        // Find the batch within the course
        $batch = Batch::query()
            ->byPeriod()
            ->where('course_id', $course->id)
            ->where('name', $batchName)
            ->first();

        if (!$batch) {
            $result['errors'][] = 'Class Arm "' . $batchName . '" not found in class "' . $courseName . '"';
            return $result;
        }

        $result['valid'] = true;
        $result['course'] = $course;
        $result['batch'] = $batch;

        return $result;
    }
}
