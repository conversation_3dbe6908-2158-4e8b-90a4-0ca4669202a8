<template>
    <FilterForm
        :init-form="initForm"
        :form="form"
        :multiple="['batches']"
        @hide="emit('hide')"
    >
        <div class="grid grid-cols-4 gap-6">
            <div class="col-span-4 sm:col-span-1">
                <BaseInput
                    type="text"
                    v-model="form.title"
                    name="title"
                    :label="$trans('exam.question_bank.props.title')"
                />
            </div>
            <div class="col-span-4 sm:col-span-1">
                <BaseSelect
                    v-model="form.subject"
                    name="subject"
                    :label="$trans('exam.question_bank.props.subject')"
                    label-prop="name"
                    value-prop="uuid"
                    :options="props.preRequisites.subjects"
                />
            </div>
            <div class="col-span-4 sm:col-span-1">
                <BaseSelect
                    v-model="form.type"
                    name="type"
                    :label="$trans('exam.question_bank.props.type')"
                    :options="props.preRequisites.types"
                    :loading="!fetchData.isLoaded"
                />
            </div>
            <div class="col-span-4 sm:col-span-1">
                <BaseSelectSearch
                    v-if="fetchData.isLoaded"
                    multiple
                    name="batches"
                    :label="$trans('exam.question_bank.props.batches')"
                    v-model="form.batches"
                    value-prop="uuid"
                    :init-search="fetchData.batches"
                    search-key="course_batch"
                    search-action="academic/batch/list"
                >
                    <template #selectedOption="slotProps">
                        {{ slotProps.value.course.name }} -
                        {{ slotProps.value.name }}
                    </template>

                    <template #listOption="slotProps">
                        {{ slotProps.option.course.nameWithTerm }} -
                        {{ slotProps.option.name }}
                    </template>
                </BaseSelectSearch>
            </div>
        </div>
    </FilterForm>
</template>

<script setup>
import { reactive, onMounted } from "vue"
import { useRoute } from "vue-router"

const route = useRoute()
const emit = defineEmits(["hide"])

const props = defineProps({
    preRequisites: {
        type: Object,
        default() {
            return {
                subjects: [],
                types: [],
                batches: [],
            }
        },
    },
})

const initForm = {
    title: "",
    subject: "",
    type: "",
    batches: [],
}

const form = reactive({ ...initForm })

const fetchData = reactive({
    batches: [],
    isLoaded: route.query.batches ? false : true,
})

onMounted(async () => {
    // Handle URL parameters for batches
    if (route.query.batches) {
        fetchData.batches = route.query.batches.split(",")
    }
    fetchData.isLoaded = true
})
</script>
