<template>
    <PageHeader
        :title="$trans('exam.question_bank.question_bank')"
        :navs="[
            { label: $trans('exam.exam'), path: 'Exam' },
            {
                label: $trans('exam.question_bank.question_bank'),
                path: 'ExamQuestionBankList',
            },
        ]"
    >
        <PageHeaderAction
            name="ExamQuestionBank"
            :title="$trans('exam.question_bank.question_bank')"
            :actions="['list']"
            :dropdown-actions="dropdownActions"
            :uuid="route.params.uuid"
        />
    </PageHeader>

    <ParentTransition appear :visibility="true">
        <ShowItem
            :init-url="initUrl"
            :uuid="route.params.uuid"
            @setItem="setItem"
            @redirectTo="router.push({ name: 'ExamQuestionBank' })"
        >
            <BaseCard v-if="questionBank.uuid">
                <template #title>
                    <div v-html="questionBank.title"></div>
                </template>
                <div class="space-y-6">
                <!-- Basic Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            {{ $trans('exam.question_bank.props.title') }}
                        </label>
                        <div class="text-gray-900" v-html="questionBank.title"></div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            {{ $trans('exam.question_bank.props.type') }}
                        </label>
                        <p class="text-gray-900">{{ questionBank.type?.label }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            {{ $trans('exam.question_bank.props.subject') }}
                        </label>
                        <p class="text-gray-900">{{ questionBank.subject?.name }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            {{ $trans('exam.question_bank.props.mark') }}
                        </label>
                        <p class="text-gray-900">{{ questionBank.mark }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            {{ $trans('exam.question_bank.props.batches') }}
                        </label>
                        <div v-if="questionBank.batches?.length > 0">
                            <span
                                v-for="batch in questionBank.batches"
                                :key="batch.uuid"
                                class="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded mr-1 mb-1"
                            >
                                {{ batch.course?.name }} - {{ batch.name }}
                            </span>
                        </div>
                        <p v-else class="text-gray-500">-</p>
                    </div>
                </div>

                <!-- Header -->
                <div v-if="questionBank.header">
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        {{ $trans('exam.question_bank.props.header') }}
                    </label>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="text-gray-900" v-html="questionBank.header"></div>
                    </div>
                </div>

                <!-- Description -->
                <div v-if="questionBank.description">
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        {{ $trans('exam.question_bank.props.description') }}
                    </label>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <p class="text-gray-900 whitespace-pre-wrap">{{ questionBank.description }}</p>
                    </div>
                </div>

                <!-- MCQ Options -->
                <div v-if="questionBank.type?.value === 'mcq' && questionBank.options?.length > 0">
                    <label class="block text-sm font-medium text-gray-700 mb-3">
                        {{ $trans('exam.question_bank.props.options') }}
                    </label>
                    <div class="space-y-2">
                        <div
                            v-for="(option, index) in questionBank.options"
                            :key="index"
                            class="flex items-center p-3 border rounded-lg"
                            :class="{
                                'bg-green-50 border-green-200': option.isCorrect,
                                'bg-gray-50': !option.isCorrect
                            }"
                        >
                            <span class="w-6 h-6 rounded-full border-2 flex items-center justify-center mr-3 text-sm font-medium"
                                :class="{
                                    'bg-green-500 border-green-500 text-white': option.isCorrect,
                                    'border-gray-300': !option.isCorrect
                                }"
                            >
                                {{ String.fromCharCode(65 + index) }}
                            </span>
                            <span class="flex-1">{{ option.title }}</span>
                            <span v-if="option.isCorrect" class="text-green-600 text-sm font-medium">
                                {{ $trans('exam.question_bank.correct_answer') }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Metadata -->
                <div class="border-t pt-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
                        <div>
                            <label class="block font-medium mb-1">
                                {{ $trans('general.created_at') }}
                            </label>
                            <p>{{ questionBank.createdAt.formatted }}</p>
                        </div>
                        <div>
                            <label class="block font-medium mb-1">
                                {{ $trans('general.updated_at') }}
                            </label>
                            <p>{{ questionBank.updatedAt.formatted }}</p>
                        </div>
                    </div>
                </div>
            </div>
                <template #footer>
                    <ShowButton>
                        <BaseButton
                            v-if="perform('question-bank:edit')"
                            design="primary"
                            @click="
                                router.push({
                                    name: 'ExamQuestionBankEdit',
                                    params: { uuid: questionBank.uuid },
                                })
                            "
                        >
                            {{ $trans('general.edit') }}
                        </BaseButton>
                    </ShowButton>
                </template>
            </BaseCard>
        </ShowItem>
    </ParentTransition>
</template>

<script>
export default {
    name: "ExamQuestionBankShow",
}
</script>

<script setup>
import { reactive } from "vue"
import { useRoute, useRouter } from "vue-router"
import { perform } from "@core/helpers/action"

const route = useRoute()
const router = useRouter()

const questionBank = reactive({})

const initUrl = "exam/questionBank/"

let dropdownActions = []
if (perform("question-bank:edit")) {
    dropdownActions.push("edit")
}
if (perform("question-bank:create")) {
    dropdownActions.push("duplicate")
}
if (perform("question-bank:delete")) {
    dropdownActions.push("delete")
}

const setItem = (data) => {
    Object.assign(questionBank, data)
}
</script>
