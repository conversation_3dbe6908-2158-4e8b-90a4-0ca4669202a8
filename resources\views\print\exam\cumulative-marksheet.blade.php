<x-print.layout type="{{ Arr::get($layout, 'column', 1) == 1 ? 'centered' : 'full-page' }}" :spacing="false">
    @foreach ($students->chunk(Arr::get($layout, 'column', 1)) as $studentPair)
        <div style="margin-top: {{ Arr::get($layout, 'margin_top', 0) }}mm; page-break-after: always;">
            <div style="display: flex; justify-content: space-between;">
                @foreach ($studentPair as $student)
                    <div style="width: {{ Arr::get($layout, 'box_width') }}; border: 1px solid black;">

                        <div class="{{ Arr::get($layout, 'watermark') ? 'watermark-container' : '' }}">
                            @if (Arr::get($layout, 'watermark'))
                                <img class="watermark-image" src="{{ url(config('config.assets.logo')) }}">
                            @endif

                            @includeFirst([
                                config('config.print.custom_path') . 'exam.header',
                                'print.exam.header',
                            ])

                            @if (view()->exists(config('config.print.custom_path') . 'exam.marksheet-subheader'))
                                @include(config('config.print.custom_path') . 'exam.marksheet-subheader', [
                                    'titles' => $titles,
                                ])
                            @else
                                <div style="padding: 10px 15px;">
                                    <table width="100%" border="0">
                                        <tr>
                                            <td colspan="2">
                                                @foreach ($titles as $title)
                                                    @if (Arr::get($title, 'label'))
                                                        <div class="{{ Arr::get($title, 'class') }}">
                                                            {{ Arr::get($title, 'label') }}
                                                        </div>
                                                    @endif
                                                @endforeach
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            @endif

                            <table class="mt-2 inner-table cellpadding" width="100%">
                                <tr>
                                    <td>{{ trans('student.props.name') }}</td>
                                    <td class="text-right">{{ $student->name }}</td>
                                    <td>{{ trans('contact.props.birth_date') }}</td>
                                    <td class="text-right">{{ \Cal::date($student->birth_date)->formatted }}</td>
                                </tr>
                                {{-- <tr>
                                    <td>{{ trans('contact.props.father_name') }}</td>
                                    <td class="text-right">{{ $student->father_name }}</td>
                                    <td>{{ trans('contact.props.mother_name') }}</td>
                                    <td class="text-right">{{ $student->mother_name }}</td>
                                </tr> --}}
                                <tr>
                                    <td>{{ trans('academic.course.course') }}</td>
                                    <td class="text-right">{{ $student->course_name . ' ' . $student->batch_name }}
                                    </td>
                                    <td>{{ trans('student.attendance.attendance') }}</td>
                                    <td class="text-right">{{ Arr::get($student->attendance, 'present') }} /
                                        {{ Arr::get($student->attendance, 'working_days') }}</td>
                                    {{-- <td>{{ trans('contact.props.contact_number') }}</td>
                                    <td class="text-right">{{ $student->contact_number }}</td> --}}
                                </tr>
                                {{-- <tr>
                                    <td>{{ trans('student.health_record.props.height') }} /
                                        {{ trans('student.health_record.props.weight') }}</td>
                                    <td class="text-right">{{ Arr::get($student->health_record, 'general.height') }}cm
                                        / {{ Arr::get($student->health_record, 'general.weight') }}kg
                                    </td>
                                    <td>{{ trans('student.attendance.attendance') }}</td>
                                    <td class="text-right">{{ Arr::get($student->attendance, 'present') }} /
                                        {{ Arr::get($student->attendance, 'working_days') }}</td>
                                </tr> --}}
                                {{-- $student->roll_number is optopnal. If it doesnt exist $student->code_number should take the whole line instead of being split between two columns --}}
                                @if (isset($student->roll_number))
                                    <tr>
                                        <td>{{ trans('student.admission.props.code_number') }}</td>
                                        <td class="text-right">{{ $student->code_number }}</td>
                                        <td>{{ trans('student.roll_number.roll_number') }}</td>
                                        <td class="text-right">{{ $student->roll_number }}</td>
                                    </tr>
                                @else
                                    <tr>
                                        <td>{{ trans('student.admission.props.code_number') }}</td>
                                        <td class="text-right" colspan="3">{{ $student->code_number }}</td>
                                    </tr>
                                @endif
                                
                                
                                {{-- <tr>
                                    <td>{{ trans('student.admission.props.code_number') }}</td>
                                    <td class="text-right">{{ $student->code_number }}</td>
                                    <td>{{ trans('student.roll_number.roll_number') }}</td>
                                    <td class="text-right">{{ $student->roll_number }}</td>
                                </tr> --}}
                            </table>

                            <table class="mt-4 inner-table font-85pc" width="100%">
                                @foreach ($student->rows as $row)
                                    <tr>
                                        @foreach ($row as $cell)
                                            <td colspan="{{ Arr::get($cell, 'colspan', 1) }}"
                                                @class([
                                                    'invisible-filler-row' => Arr::get($cell, 'empty'),
                                                    'text-center' => Arr::get($cell, 'align') == 'center',
                                                    'font-110pc' => Arr::get($cell, 'font-size') == 'lg',
                                                    'font-120pc' => Arr::get($cell, 'font-size') == 'xl',
                                                    'py-2' => Arr::get($cell, 'font-size') == 'xl',
                                                    'font-weight-bold' => Arr::get($cell, 'bold'),
                                                ])
                                                @if (Arr::get($cell, 'empty'))
                                                    style="border: none !important; padding: 0 !important; height: 0 !important;"
                                                @endif
                                                rowspan="{{ Arr::get($cell, 'rowspan', 1) }}">
                                                {{ Arr::get($cell, 'label') }}

                                                @if (Arr::get($cell, 'blank'))
                                                    &nbsp;
                                                @endif
                                            </td>
                                        @endforeach
                                    </tr>
                                @endforeach

                                @foreach ($student->gradingRows as $row)
                                    <tr>
                                        @foreach ($row as $cell)
                                            <td colspan="{{ Arr::get($cell, 'colspan', 1) }}"
                                                @class([
                                                    'text-center' => Arr::get($cell, 'align') == 'center',
                                                    'font-weight-bold' => Arr::get($cell, 'bold'),
                                                ])
                                                rowspan="{{ Arr::get($cell, 'rowspan', 1) }}">
                                                {{ Arr::get($cell, 'label') }}
                                            </td>
                                        @endforeach
                                    </tr>
                                @endforeach
                            </table>

                            <table class="mt-4 inner-table font-80pc" width="100%">
                                @foreach ($student->observationRows as $row)
                                    <tr>
                                        @foreach ($row as $cell)
                                            <td colspan="{{ Arr::get($cell, 'colspan', 1) }}"
                                                @class([
                                                    'text-center' => Arr::get($cell, 'align') == 'center',
                                                    'font-weight-bold' => Arr::get($cell, 'bold'),
                                                ])
                                                rowspan="{{ Arr::get($cell, 'rowspan', 1) }}">
                                                {{ Arr::get($cell, 'label') }}
                                            </td>
                                        @endforeach
                                    </tr>
                                @endforeach
                            </table>

                            {{-- Comments Section - Full Width --}}
                            <table class="mt-4 inner-table" width="100%" border="1">
                                <tr>
                                    <td style="padding: 10px;">
                                        <table width="100%" border="1" class="inner-table">
                                            <tr>
                                                <td style="padding: 8px; font-weight: bold; background-color: #f5f5f5;">
                                                    {{ config('config.exam.comment_behavioural_label') ?: trans('exam.comment_behavioural') }}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="padding: 15px; min-height: 80px; vertical-align: top;">
                                                    {{ Arr::get($student->comment, 'result') }}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="padding: 8px; font-weight: bold; background-color: #f5f5f5;">
                                                    {{ config('config.exam.comment_label') ?: trans('exam.comment') }}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="padding: 15px; min-height: 80px; vertical-align: top;">
                                                    {{ Arr::get($student->comment, 'comment') }}
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>

                            {{-- Grade Details Section - Separate Table Below --}}
                            @if (Arr::get($params, 'show_grade_detail'))
                                <table class="mt-4 inner-table" width="100%" border="1">
                                    <tr>
                                        <td style="padding: 10px;">
                                            <h2 class="sub-heading">{{ trans('exam.grade.grade') }}</h2>
                                            <table border="1" class="mt-4 table font-90pc" width="100%">
                                                {{-- <tr style="background-color: #f5f5f5;">
                                                    <th style="padding: 8px; text-align: center;">{{ trans('exam.grade.code') }}</th>
                                                    <th style="padding: 8px; text-align: center;">{{ trans('exam.grade.range') }}</th>
                                                    <th style="padding: 8px; text-align: center;">{{ trans('exam.grade.label') }}</th>
                                                </tr> --}}
                                                @foreach ($grade->records as $record)
                                                    <tr>
                                                        <td style="padding: 8px; text-align: center;">{{ Arr::get($record, 'code') }}</td>
                                                        <td style="padding: 8px; text-align: center;">{{ Arr::get($record, 'min_score') }} - {{ Arr::get($record, 'max_score') }}</td>
                                                        <td style="padding: 8px; text-align: center;">{{ Arr::get($record, 'label') }}</td>
                                                    </tr>
                                                @endforeach
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            @endif

                            @includeFirst(
                                [config('config.print.custom_path') . 'exam.signatory', 'print.exam.signatory'],
                                ['layout' => $layout, 'margin' => 'mt-32']
                            )

                            @if (Arr::get($layout, 'show_print_date_time'))
                                <div class="mt-4" style="padding-left: 10px; padding-right: 10px;">
                                    <p>{{ trans('general.printed_at') }}: {{ \Cal::dateTime(now())->formatted }}
                                    </p>
                                </div>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    @endforeach
</x-print.layout>
